const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const morgan = require('morgan');
require('dotenv').config(); // Load environment variables

// Create Express app
const app = express();

// Middleware (like security guards and helpers)
app.use(helmet()); // Security guard - adds security headers
app.use(cors()); // Allows frontend to connect from different port
app.use(morgan('combined')); // Logger - keeps track of all requests
app.use(express.json()); // Understands JSON data from requests

// Our first route - like a door to our app
app.get('/api/hello', (req, res) => {
  res.json({
    message: 'Welcome to SHG Management System!',
    status: 'Server is running perfectly!',
    timestamp: new Date().toISOString(),
    version: '1.0.0'
  });
});

// Health check route
app.get('/api/health', (req, res) => {
  res.json({
    status: 'OK',
    uptime: process.uptime(),
    timestamp: new Date().toISOString()
  });
});

// Handle 404 routes (before error handler)
app.use('*', (req, res) => {
  res.status(404).json({
    message: 'Route not found',
    availableRoutes: [
      'GET /api/hello',
      'GET /api/health'
    ]
  });
});

// Error handling middleware (must be last)
app.use((err, req, res, next) => {
  console.error('❌ Server Error:', err.stack);
  res.status(500).json({
    message: 'Something went wrong!',
    error: process.env.NODE_ENV === 'development' ? err.message : 'Internal server error'
  });
});

// Start the server
const PORT = process.env.PORT || 5000;
app.listen(PORT, () => {
  console.log(`🚀 SHG Management Server is running on http://localhost:${PORT}`);
  console.log(`📝 Test API: http://localhost:${PORT}/api/hello`);
  console.log(`💚 Health Check: http://localhost:${PORT}/api/health`);
  console.log(`📅 Started at: ${new Date().toISOString()}`);
});
