# Step-by-Step Implementation Guide for SHG Management System

## 🎯 Complete Development Roadmap (25 Days)

### Week 1: Foundation Setup

## Day 1: Environment Setup
**Goal**: Set up development environment and project structure

### Step 1: Install Required Software
```bash
# Install Node.js (includes npm)
# Download from: https://nodejs.org/

# Verify installation
node --version  # Should show v18+ 
npm --version   # Should show v9+

# Install Git (for version control)
# Download from: https://git-scm.com/
git --version
```

### Step 2: Create Project Structure
```bash
# Create main project directory
mkdir shg-management-system
cd shg-management-system

# Create frontend (React app)
npm create vite@latest shg-frontend -- --template react
cd shg-frontend
npm install

# Go back and create backend
cd ..
mkdir shg-backend
cd shg-backend
npm init -y
```

### Step 3: Install Backend Dependencies
```bash
# Core dependencies
npm install express mongoose bcryptjs jsonwebtoken cors dotenv helmet morgan

# Development dependencies
npm install --save-dev nodemon concurrently
```

**Explanation for Student:**
- `express`: Web framework for creating APIs
- `mongoose`: MongoDB object modeling (makes database easier)
- `bcryptjs`: Password encryption (security)
- `jsonwebtoken`: User authentication tokens
- `cors`: Allows frontend to talk to backend
- `dotenv`: Manages environment variables (secrets)
- `helmet`: Security middleware
- `morgan`: Logs HTTP requests (debugging)

---

## Day 2: Database Setup

### Step 1: MongoDB Atlas Setup
1. Go to [mongodb.com/atlas](https://mongodb.com/atlas)
2. Create free account
3. Create new cluster (choose free tier)
4. Create database user
5. Whitelist IP address (0.0.0.0/0 for development)
6. Get connection string

### Step 2: Environment Configuration
```bash
# Create .env file in shg-backend directory
touch .env
```

```env
# .env file content
MONGODB_URI=mongodb+srv://username:<EMAIL>/shg_management
JWT_SECRET=your_super_secret_key_here_make_it_long_and_random
PORT=5000
NODE_ENV=development
```

### Step 3: Database Connection
```javascript
// shg-backend/config/database.js
const mongoose = require('mongoose');

const connectDB = async () => {
  try {
    const conn = await mongoose.connect(process.env.MONGODB_URI);
    console.log(`✅ MongoDB Connected: ${conn.connection.host}`);
  } catch (error) {
    console.error('❌ Database connection failed:', error.message);
    process.exit(1);
  }
};

module.exports = connectDB;
```

**Test Database Connection:**
```javascript
// shg-backend/test-db.js
require('dotenv').config();
const connectDB = require('./config/database');

connectDB().then(() => {
  console.log('Database test successful!');
  process.exit(0);
});
```

Run: `node test-db.js`

---

## Day 3-4: Basic Server Setup

### Step 1: Create Basic Express Server
```javascript
// shg-backend/server.js
const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const morgan = require('morgan');
require('dotenv').config();

const connectDB = require('./config/database');

const app = express();

// Connect to database
connectDB();

// Middleware
app.use(helmet()); // Security headers
app.use(cors()); // Enable CORS
app.use(morgan('combined')); // Logging
app.use(express.json()); // Parse JSON bodies

// Test route
app.get('/api/test', (req, res) => {
  res.json({ message: 'SHG Management API is running!' });
});

// Error handling middleware
app.use((err, req, res, next) => {
  console.error(err.stack);
  res.status(500).json({ message: 'Something went wrong!' });
});

const PORT = process.env.PORT || 5000;
app.listen(PORT, () => {
  console.log(`🚀 Server running on port ${PORT}`);
});
```

### Step 2: Update package.json Scripts
```json
{
  "scripts": {
    "start": "node server.js",
    "dev": "nodemon server.js",
    "test": "echo \"Error: no test specified\" && exit 1"
  }
}
```

**Test Server:**
```bash
npm run dev
# Visit: http://localhost:5000/api/test
```

---

## Day 5-7: Authentication System

### Step 1: User Model
```javascript
// shg-backend/models/User.js
const mongoose = require('mongoose');
const bcrypt = require('bcryptjs');

const userSchema = new mongoose.Schema({
  username: {
    type: String,
    required: [true, 'Username is required'],
    unique: true,
    trim: true,
    minlength: [3, 'Username must be at least 3 characters']
  },
  email: {
    type: String,
    required: [true, 'Email is required'],
    unique: true,
    lowercase: true,
    match: [/^\w+([.-]?\w+)*@\w+([.-]?\w+)*(\.\w{2,3})+$/, 'Please enter a valid email']
  },
  password: {
    type: String,
    required: [true, 'Password is required'],
    minlength: [6, 'Password must be at least 6 characters']
  },
  role: {
    type: String,
    enum: ['admin', 'member'],
    default: 'member'
  },
  shgGroup: {
    type: String,
    required: [true, 'SHG Group is required']
  },
  isActive: {
    type: Boolean,
    default: true
  }
}, {
  timestamps: true // Automatically adds createdAt and updatedAt
});

// Hash password before saving
userSchema.pre('save', async function(next) {
  // Only hash if password is modified
  if (!this.isModified('password')) return next();
  
  // Hash password with cost of 12
  this.password = await bcrypt.hash(this.password, 12);
  next();
});

// Instance method to check password
userSchema.methods.comparePassword = async function(candidatePassword) {
  return await bcrypt.compare(candidatePassword, this.password);
};

module.exports = mongoose.model('User', userSchema);
```

### Step 2: Authentication Routes
```javascript
// shg-backend/routes/auth.js
const express = require('express');
const jwt = require('jsonwebtoken');
const User = require('../models/User');

const router = express.Router();

// Generate JWT token
const generateToken = (userId, role) => {
  return jwt.sign(
    { userId, role },
    process.env.JWT_SECRET,
    { expiresIn: '7d' }
  );
};

// @route   POST /api/auth/register
// @desc    Register new user
// @access  Public
router.post('/register', async (req, res) => {
  try {
    const { username, email, password, shgGroup, role } = req.body;

    // Check if user already exists
    const existingUser = await User.findOne({
      $or: [{ email }, { username }]
    });

    if (existingUser) {
      return res.status(400).json({
        success: false,
        message: 'User with this email or username already exists'
      });
    }

    // Create new user
    const user = new User({
      username,
      email,
      password,
      shgGroup,
      role: role || 'member'
    });

    await user.save();

    // Generate token
    const token = generateToken(user._id, user.role);

    res.status(201).json({
      success: true,
      message: 'User registered successfully',
      token,
      user: {
        id: user._id,
        username: user.username,
        email: user.email,
        role: user.role,
        shgGroup: user.shgGroup
      }
    });

  } catch (error) {
    console.error('Registration error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error during registration',
      error: error.message
    });
  }
});

// @route   POST /api/auth/login
// @desc    Login user
// @access  Public
router.post('/login', async (req, res) => {
  try {
    const { email, password } = req.body;

    // Validate input
    if (!email || !password) {
      return res.status(400).json({
        success: false,
        message: 'Please provide email and password'
      });
    }

    // Find user and include password for comparison
    const user = await User.findOne({ email }).select('+password');

    if (!user) {
      return res.status(401).json({
        success: false,
        message: 'Invalid email or password'
      });
    }

    // Check if user is active
    if (!user.isActive) {
      return res.status(401).json({
        success: false,
        message: 'Account is deactivated'
      });
    }

    // Check password
    const isPasswordCorrect = await user.comparePassword(password);

    if (!isPasswordCorrect) {
      return res.status(401).json({
        success: false,
        message: 'Invalid email or password'
      });
    }

    // Generate token
    const token = generateToken(user._id, user.role);

    res.json({
      success: true,
      message: 'Login successful',
      token,
      user: {
        id: user._id,
        username: user.username,
        email: user.email,
        role: user.role,
        shgGroup: user.shgGroup
      }
    });

  } catch (error) {
    console.error('Login error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error during login',
      error: error.message
    });
  }
});

module.exports = router;
```

### Step 3: Authentication Middleware
```javascript
// shg-backend/middleware/auth.js
const jwt = require('jsonwebtoken');
const User = require('../models/User');

const authenticateToken = async (req, res, next) => {
  try {
    // Get token from header
    const authHeader = req.headers.authorization;
    const token = authHeader && authHeader.split(' ')[1]; // Bearer TOKEN

    if (!token) {
      return res.status(401).json({
        success: false,
        message: 'Access token required'
      });
    }

    // Verify token
    const decoded = jwt.verify(token, process.env.JWT_SECRET);

    // Get user from database
    const user = await User.findById(decoded.userId);

    if (!user || !user.isActive) {
      return res.status(401).json({
        success: false,
        message: 'Invalid token or user not found'
      });
    }

    // Add user to request object
    req.user = user;
    next();

  } catch (error) {
    console.error('Auth middleware error:', error);
    res.status(401).json({
      success: false,
      message: 'Invalid token'
    });
  }
};

// Admin only middleware
const requireAdmin = (req, res, next) => {
  if (req.user.role !== 'admin') {
    return res.status(403).json({
      success: false,
      message: 'Admin access required'
    });
  }
  next();
};

module.exports = { authenticateToken, requireAdmin };
```

### Step 4: Update Server with Auth Routes
```javascript
// Add to shg-backend/server.js
const authRoutes = require('./routes/auth');

// Routes
app.use('/api/auth', authRoutes);

// Protected route example
app.get('/api/protected', authenticateToken, (req, res) => {
  res.json({
    message: 'This is a protected route',
    user: req.user.username
  });
});
```

**Test Authentication:**
```bash
# Test registration
curl -X POST http://localhost:5000/api/auth/register \
  -H "Content-Type: application/json" \
  -d '{
    "username": "admin1",
    "email": "<EMAIL>",
    "password": "password123",
    "shgGroup": "Mahila Mandal Group 1",
    "role": "admin"
  }'

# Test login
curl -X POST http://localhost:5000/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "password123"
  }'
```

---

## Week 2: Core Features

## Day 8-10: Member Management (CRUD)

### Step 1: Member Model
```javascript
// shg-backend/models/Member.js
const mongoose = require('mongoose');

const memberSchema = new mongoose.Schema({
  memberId: {
    type: String,
    required: true,
    unique: true,
    uppercase: true
  },
  name: {
    type: String,
    required: [true, 'Member name is required'],
    trim: true
  },
  fatherName: {
    type: String,
    required: [true, 'Father name is required'],
    trim: true
  },
  address: {
    type: String,
    required: [true, 'Address is required']
  },
  phoneNumber: {
    type: String,
    required: [true, 'Phone number is required'],
    match: [/^[6-9]\d{9}$/, 'Please enter a valid Indian mobile number']
  },
  aadharNumber: {
    type: String,
    required: [true, 'Aadhar number is required'],
    unique: true,
    match: [/^\d{12}$/, 'Aadhar number must be 12 digits']
  },
  bankAccount: {
    accountNumber: String,
    ifscCode: String,
    bankName: String
  },
  joinDate: {
    type: Date,
    default: Date.now
  },
  isActive: {
    type: Boolean,
    default: true
  },
  shgGroup: {
    type: String,
    required: true
  },
  savings: {
    type: Number,
    default: 0,
    min: 0
  },
  totalLoans: {
    type: Number,
    default: 0,
    min: 0
  },
  profilePhoto: String
}, {
  timestamps: true
});

// Generate member ID before saving
memberSchema.pre('save', async function(next) {
  if (!this.memberId) {
    const count = await mongoose.model('Member').countDocuments({ shgGroup: this.shgGroup });
    this.memberId = `${this.shgGroup.replace(/\s+/g, '').toUpperCase()}_${(count + 1).toString().padStart(3, '0')}`;
  }
  next();
});

// Index for better search performance
memberSchema.index({ name: 'text', fatherName: 'text', memberId: 'text' });
memberSchema.index({ shgGroup: 1, isActive: 1 });

module.exports = mongoose.model('Member', memberSchema);
```

*[This guide continues with detailed implementation steps for each feature...]*

## 🎯 Key Learning Points for Your Student

1. **Progressive Development**: Each day builds on the previous day's work
2. **Database Integration**: Happens naturally as features are added
3. **Testing**: Each feature is tested before moving to the next
4. **Real-world Skills**: Uses industry-standard practices and tools
5. **Problem-solving**: Explains why each technology choice was made

This step-by-step approach ensures your student understands not just what to do, but why each step is necessary and how it fits into the bigger picture of building a professional web application.
