import { useState, useEffect } from 'react'
import axios from 'axios'
import './App.css'

function App() {
  const [backendStatus, setBackendStatus] = useState(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState(null)

  // Test connection to backend
  useEffect(() => {
    const testBackendConnection = async () => {
      try {
        setLoading(true)
        const response = await axios.get('http://localhost:5000/api/hello')
        setBackendStatus(response.data)
        setError(null)
      } catch (err) {
        setError('Failed to connect to backend. Make sure the server is running on port 5000.')
        console.error('Backend connection error:', err)
      } finally {
        setLoading(false)
      }
    }

    testBackendConnection()
  }, [])

  return (
    <div className="min-h-screen bg-gray-100 py-8">
      <div className="max-w-4xl mx-auto px-4">
        {/* Header */}
        <div className="text-center mb-8">
          <h1 className="text-4xl font-bold text-gray-800 mb-2">
            🏦 SHG Management System
          </h1>
          <p className="text-gray-600">
            Self Help Group Financial Management Platform
          </p>
        </div>

        {/* Backend Connection Status */}
        <div className="bg-white rounded-lg shadow-md p-6 mb-6">
          <h2 className="text-2xl font-semibold text-gray-800 mb-4">
            🔗 Backend Connection Status
          </h2>

          {loading && (
            <div className="text-blue-600">
              <span className="animate-pulse">🔄 Connecting to backend...</span>
            </div>
          )}

          {error && (
            <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
              <strong>❌ Connection Failed:</strong> {error}
            </div>
          )}

          {backendStatus && (
            <div className="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded">
              <h3 className="font-semibold">✅ Backend Connected Successfully!</h3>
              <div className="mt-2 text-sm">
                <p><strong>Message:</strong> {backendStatus.message}</p>
                <p><strong>Status:</strong> {backendStatus.status}</p>
                <p><strong>Version:</strong> {backendStatus.version}</p>
                <p><strong>Timestamp:</strong> {new Date(backendStatus.timestamp).toLocaleString()}</p>
              </div>
            </div>
          )}
        </div>

        {/* Features Preview */}
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
          <div className="bg-white rounded-lg shadow-md p-6">
            <h3 className="text-xl font-semibold text-gray-800 mb-3">
              👥 Member Management
            </h3>
            <p className="text-gray-600 mb-4">
              Add, edit, and manage SHG member information, savings, and loans.
            </p>
            <button className="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
              Coming Soon
            </button>
          </div>

          <div className="bg-white rounded-lg shadow-md p-6">
            <h3 className="text-xl font-semibold text-gray-800 mb-3">
              💰 Financial Tracking
            </h3>
            <p className="text-gray-600 mb-4">
              Track savings, loans, interest, and group financial health.
            </p>
            <button className="bg-green-500 hover:bg-green-700 text-white font-bold py-2 px-4 rounded">
              Coming Soon
            </button>
          </div>

          <div className="bg-white rounded-lg shadow-md p-6">
            <h3 className="text-xl font-semibold text-gray-800 mb-3">
              📊 Reports & Analytics
            </h3>
            <p className="text-gray-600 mb-4">
              Generate reports and visualize group performance with charts.
            </p>
            <button className="bg-purple-500 hover:bg-purple-700 text-white font-bold py-2 px-4 rounded">
              Coming Soon
            </button>
          </div>
        </div>

        {/* Development Status */}
        <div className="mt-8 bg-blue-50 border border-blue-200 rounded-lg p-6">
          <h3 className="text-lg font-semibold text-blue-800 mb-3">
            🚀 Development Progress
          </h3>
          <div className="space-y-2">
            <div className="flex items-center">
              <span className="text-green-600 mr-2">✅</span>
              <span>Backend server setup with Express.js</span>
            </div>
            <div className="flex items-center">
              <span className="text-green-600 mr-2">✅</span>
              <span>Frontend React app with Vite</span>
            </div>
            <div className="flex items-center">
              <span className="text-green-600 mr-2">✅</span>
              <span>Frontend-Backend connection established</span>
            </div>
            <div className="flex items-center">
              <span className="text-yellow-600 mr-2">🔄</span>
              <span>Authentication system (In Progress)</span>
            </div>
            <div className="flex items-center">
              <span className="text-gray-400 mr-2">⏳</span>
              <span>Member management CRUD operations</span>
            </div>
            <div className="flex items-center">
              <span className="text-gray-400 mr-2">⏳</span>
              <span>Financial tracking features</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default App
