# Phase-by-Phase Development Guide for SHG Management System

## 🎯 Phase 1: Project Setup & Basic Structure (Week 1)

### What We'll Do:
1. Set up the development environment
2. Create project structure
3. Initialize React frontend and Node.js backend
4. Set up MongoDB database

### Step-by-Step Implementation:

#### 1.1 Initialize Frontend (React with Vite)
```bash
# Create React app with Vite (faster than create-react-app)
npm create vite@latest shg-frontend -- --template react
cd shg-frontend
npm install

# Install additional dependencies
npm install react-router-dom axios tailwindcss
npm install @headlessui/react @heroicons/react
```

**Why Vite?**
- Much faster development server
- Better build performance
- Modern tooling with hot module replacement

#### 1.2 Initialize Backend (Node.js + Express)
```bash
# Create backend directory
mkdir shg-backend
cd shg-backend
npm init -y

# Install core dependencies
npm install express mongoose bcryptjs jsonwebtoken
npm install cors dotenv helmet morgan
npm install --save-dev nodemon
```

**Package Explanations:**
- `express`: Web framework for APIs
- `mongoose`: MongoDB object modeling
- `bcryptjs`: Password hashing
- `jsonwebtoken`: JWT token creation/verification
- `cors`: Cross-origin resource sharing
- `dotenv`: Environment variables
- `helmet`: Security middleware
- `morgan`: HTTP request logger

#### 1.3 Set Up MongoDB
```bash
# Option 1: MongoDB Atlas (Cloud - Recommended for beginners)
# Sign up at mongodb.com/atlas
# Create cluster and get connection string

# Option 2: Local MongoDB installation
# Download from mongodb.com/try/download/community
```

**Why MongoDB Atlas?**
- No local installation needed
- Automatic backups
- Built-in security
- Free tier available

### 1.4 Project Structure
```
shg-management-system/
├── shg-frontend/          # React application
│   ├── src/
│   │   ├── components/    # Reusable UI components
│   │   ├── pages/         # Page components
│   │   ├── hooks/         # Custom React hooks
│   │   ├── services/      # API calls
│   │   └── utils/         # Helper functions
├── shg-backend/           # Node.js API
│   ├── models/            # MongoDB schemas
│   ├── routes/            # API endpoints
│   ├── middleware/        # Custom middleware
│   ├── controllers/       # Business logic
│   └── utils/             # Helper functions
└── docs/                  # Documentation
```

## 🔐 Phase 2: Authentication System (Week 2)

### What We'll Build:
1. User registration and login
2. JWT token generation and validation
3. Protected routes
4. Password hashing and security

### Why Authentication First?
- Security foundation for entire app
- Required for all other features
- Establishes user context

### 2.1 Backend Authentication

#### User Model (MongoDB Schema)
```javascript
// models/User.js
const mongoose = require('mongoose');
const bcrypt = require('bcryptjs');

const userSchema = new mongoose.Schema({
  username: { type: String, required: true, unique: true },
  email: { type: String, required: true, unique: true },
  password: { type: String, required: true },
  role: { type: String, enum: ['admin', 'member'], default: 'member' },
  shgGroup: { type: String, required: true },
  createdAt: { type: Date, default: Date.now }
});

// Hash password before saving
userSchema.pre('save', async function(next) {
  if (!this.isModified('password')) return next();
  this.password = await bcrypt.hash(this.password, 12);
  next();
});

module.exports = mongoose.model('User', userSchema);
```

#### Authentication Routes
```javascript
// routes/auth.js
const express = require('express');
const jwt = require('jsonwebtoken');
const bcrypt = require('bcryptjs');
const User = require('../models/User');

const router = express.Router();

// Register new user
router.post('/register', async (req, res) => {
  try {
    const { username, email, password, shgGroup } = req.body;
    
    // Check if user exists
    const existingUser = await User.findOne({ 
      $or: [{ email }, { username }] 
    });
    
    if (existingUser) {
      return res.status(400).json({ message: 'User already exists' });
    }
    
    // Create new user
    const user = new User({ username, email, password, shgGroup });
    await user.save();
    
    // Generate JWT token
    const token = jwt.sign(
      { userId: user._id, role: user.role },
      process.env.JWT_SECRET,
      { expiresIn: '7d' }
    );
    
    res.status(201).json({
      message: 'User created successfully',
      token,
      user: { id: user._id, username, email, role: user.role }
    });
  } catch (error) {
    res.status(500).json({ message: 'Server error', error: error.message });
  }
});

// Login user
router.post('/login', async (req, res) => {
  try {
    const { email, password } = req.body;
    
    // Find user
    const user = await User.findOne({ email });
    if (!user) {
      return res.status(400).json({ message: 'Invalid credentials' });
    }
    
    // Check password
    const isMatch = await bcrypt.compare(password, user.password);
    if (!isMatch) {
      return res.status(400).json({ message: 'Invalid credentials' });
    }
    
    // Generate token
    const token = jwt.sign(
      { userId: user._id, role: user.role },
      process.env.JWT_SECRET,
      { expiresIn: '7d' }
    );
    
    res.json({
      message: 'Login successful',
      token,
      user: { id: user._id, username: user.username, email, role: user.role }
    });
  } catch (error) {
    res.status(500).json({ message: 'Server error', error: error.message });
  }
});

module.exports = router;
```

### 2.2 Frontend Authentication

#### Login Component
```jsx
// components/Login.jsx
import { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import axios from 'axios';

const Login = () => {
  const [formData, setFormData] = useState({ email: '', password: '' });
  const [loading, setLoading] = useState(false);
  const navigate = useNavigate();

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    
    try {
      const response = await axios.post('/api/auth/login', formData);
      
      // Store token in localStorage
      localStorage.setItem('token', response.data.token);
      localStorage.setItem('user', JSON.stringify(response.data.user));
      
      // Redirect to dashboard
      navigate('/dashboard');
    } catch (error) {
      alert(error.response?.data?.message || 'Login failed');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50">
      <div className="max-w-md w-full space-y-8">
        <div>
          <h2 className="mt-6 text-center text-3xl font-extrabold text-gray-900">
            SHG Management Login
          </h2>
        </div>
        <form className="mt-8 space-y-6" onSubmit={handleSubmit}>
          <div>
            <input
              type="email"
              required
              className="appearance-none rounded-md relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
              placeholder="Email address"
              value={formData.email}
              onChange={(e) => setFormData({...formData, email: e.target.value})}
            />
          </div>
          <div>
            <input
              type="password"
              required
              className="appearance-none rounded-md relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
              placeholder="Password"
              value={formData.password}
              onChange={(e) => setFormData({...formData, password: e.target.value})}
            />
          </div>
          <div>
            <button
              type="submit"
              disabled={loading}
              className="group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50"
            >
              {loading ? 'Signing in...' : 'Sign in'}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default Login;
```

## 🔄 Phase 3: Member Management (CRUD) (Week 3)

### What We'll Build:
1. Add new members
2. View member list
3. Edit member information
4. Delete members
5. Search functionality

### Why CRUD Operations?
- Foundation of any data management system
- Essential for member database maintenance
- Teaches core database operations

### 3.1 Member Model
```javascript
// models/Member.js
const mongoose = require('mongoose');

const memberSchema = new mongoose.Schema({
  memberId: { type: String, required: true, unique: true },
  name: { type: String, required: true },
  fatherName: { type: String, required: true },
  address: { type: String, required: true },
  phoneNumber: { type: String, required: true },
  aadharNumber: { type: String, required: true, unique: true },
  bankAccount: {
    accountNumber: String,
    ifscCode: String,
    bankName: String
  },
  joinDate: { type: Date, default: Date.now },
  isActive: { type: Boolean, default: true },
  shgGroup: { type: String, required: true },
  savings: { type: Number, default: 0 },
  loans: [{
    amount: Number,
    interestRate: Number,
    issueDate: Date,
    dueDate: Date,
    status: { type: String, enum: ['active', 'paid', 'overdue'], default: 'active' }
  }],
  createdAt: { type: Date, default: Date.now },
  updatedAt: { type: Date, default: Date.now }
});

module.exports = mongoose.model('Member', memberSchema);
```

*[Continue with more phases...]*
