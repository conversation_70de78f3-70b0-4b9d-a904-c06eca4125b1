# STEP 2: Authentication System - Users Can Register & Login! 🔐

## What We're Building Now
"Now we'll add user registration and login. Think of it like creating membership cards for our SHG system - only registered users can access the member data!"

---

## 🎯 STEP 2A: Create User Model (10 minutes)

### Create User Database Schema
```javascript
// shg-backend/models/User.js
const mongoose = require('mongoose');
const bcrypt = require('bcryptjs');

// Define what a User looks like in our database
const userSchema = new mongoose.Schema({
  username: {
    type: String,
    required: [true, 'Username is required'],
    unique: true,
    trim: true,
    minlength: [3, 'Username must be at least 3 characters'],
    maxlength: [20, 'Username cannot exceed 20 characters']
  },
  email: {
    type: String,
    required: [true, 'Email is required'],
    unique: true,
    lowercase: true,
    match: [/^\w+([.-]?\w+)*@\w+([.-]?\w+)*(\.\w{2,3})+$/, 'Please enter a valid email']
  },
  password: {
    type: String,
    required: [true, 'Password is required'],
    minlength: [6, 'Password must be at least 6 characters']
  },
  role: {
    type: String,
    enum: ['admin', 'member'],
    default: 'member'
  },
  shgGroup: {
    type: String,
    required: [true, 'SHG Group name is required']
  },
  isActive: {
    type: Boolean,
    default: true
  }
}, {
  timestamps: true // Automatically adds createdAt and updatedAt
});

// Hash password before saving to database
userSchema.pre('save', async function(next) {
  // Only hash if password is new or modified
  if (!this.isModified('password')) return next();
  
  console.log('🔒 Hashing password for user:', this.username);
  
  // Hash password with cost of 12 (very secure)
  this.password = await bcrypt.hash(this.password, 12);
  next();
});

// Method to check if entered password matches stored password
userSchema.methods.comparePassword = async function(candidatePassword) {
  console.log('🔍 Checking password for user:', this.username);
  return await bcrypt.compare(candidatePassword, this.password);
};

// Don't return password in JSON responses
userSchema.methods.toJSON = function() {
  const userObject = this.toObject();
  delete userObject.password;
  return userObject;
};

module.exports = mongoose.model('User', userSchema);
```

**Explain to Student:**
"This is like creating a form template for user information. We're telling MongoDB:
- Username must be unique (no duplicates)
- Email must be valid format
- Password gets encrypted automatically
- We track when users are created
- Passwords are never shown in responses (security!)"

---

## 🎯 STEP 2B: Create Authentication Controller (15 minutes)

### Create the business logic
```javascript
// shg-backend/controllers/authController.js
const User = require('../models/User');
const jwt = require('jsonwebtoken');

// Helper function to create JWT token
const generateToken = (userId, role, shgGroup) => {
  return jwt.sign(
    { 
      userId, 
      role, 
      shgGroup 
    },
    process.env.JWT_SECRET,
    { expiresIn: '7d' } // Token expires in 7 days
  );
};

// Register new user
const registerUser = async (req, res) => {
  try {
    console.log('📝 New user registration attempt:', req.body.username);
    
    const { username, email, password, shgGroup, role } = req.body;

    // Check if user already exists
    const existingUser = await User.findOne({
      $or: [{ email }, { username }]
    });

    if (existingUser) {
      console.log('❌ User already exists:', existingUser.username);
      return res.status(400).json({
        success: false,
        message: 'User with this email or username already exists'
      });
    }

    // Create new user
    const user = new User({
      username,
      email,
      password, // Will be hashed automatically by the model
      shgGroup,
      role: role || 'member'
    });

    await user.save();
    console.log('✅ User created successfully:', user.username);

    // Generate JWT token
    const token = generateToken(user._id, user.role, user.shgGroup);

    res.status(201).json({
      success: true,
      message: 'User registered successfully',
      token,
      user: {
        id: user._id,
        username: user.username,
        email: user.email,
        role: user.role,
        shgGroup: user.shgGroup
      }
    });

  } catch (error) {
    console.error('❌ Registration error:', error.message);
    res.status(500).json({
      success: false,
      message: 'Server error during registration',
      error: error.message
    });
  }
};

// Login user
const loginUser = async (req, res) => {
  try {
    console.log('🔑 Login attempt for:', req.body.email);
    
    const { email, password } = req.body;

    // Validate input
    if (!email || !password) {
      return res.status(400).json({
        success: false,
        message: 'Please provide email and password'
      });
    }

    // Find user (include password for comparison)
    const user = await User.findOne({ email }).select('+password');

    if (!user) {
      console.log('❌ User not found:', email);
      return res.status(401).json({
        success: false,
        message: 'Invalid email or password'
      });
    }

    // Check if user is active
    if (!user.isActive) {
      console.log('❌ Inactive user login attempt:', email);
      return res.status(401).json({
        success: false,
        message: 'Account is deactivated'
      });
    }

    // Check password
    const isPasswordCorrect = await user.comparePassword(password);

    if (!isPasswordCorrect) {
      console.log('❌ Wrong password for:', email);
      return res.status(401).json({
        success: false,
        message: 'Invalid email or password'
      });
    }

    console.log('✅ Login successful for:', user.username);

    // Generate token
    const token = generateToken(user._id, user.role, user.shgGroup);

    res.json({
      success: true,
      message: 'Login successful',
      token,
      user: {
        id: user._id,
        username: user.username,
        email: user.email,
        role: user.role,
        shgGroup: user.shgGroup
      }
    });

  } catch (error) {
    console.error('❌ Login error:', error.message);
    res.status(500).json({
      success: false,
      message: 'Server error during login',
      error: error.message
    });
  }
};

// Get current user info
const getCurrentUser = async (req, res) => {
  try {
    // req.user is set by auth middleware
    const user = await User.findById(req.user.userId);
    
    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }

    res.json({
      success: true,
      user: {
        id: user._id,
        username: user.username,
        email: user.email,
        role: user.role,
        shgGroup: user.shgGroup,
        createdAt: user.createdAt
      }
    });
  } catch (error) {
    console.error('❌ Get user error:', error.message);
    res.status(500).json({
      success: false,
      message: 'Server error',
      error: error.message
    });
  }
};

module.exports = {
  registerUser,
  loginUser,
  getCurrentUser
};
```

**Explain to Student:**
"This controller is like the brain of our authentication:
1. `registerUser`: Creates new accounts and encrypts passwords
2. `loginUser`: Checks if email/password match and gives access token
3. `getCurrentUser`: Shows user info when they're logged in
4. JWT tokens are like digital ID cards that expire in 7 days"

---

## 🎯 STEP 2C: Create Authentication Middleware (10 minutes)

### Create security guard for protected routes
```javascript
// shg-backend/middleware/auth.js
const jwt = require('jsonwebtoken');
const User = require('../models/User');

// Middleware to check if user is authenticated
const authenticateToken = async (req, res, next) => {
  try {
    // Get token from header
    const authHeader = req.headers.authorization;
    const token = authHeader && authHeader.split(' ')[1]; // Bearer TOKEN

    if (!token) {
      console.log('❌ No token provided');
      return res.status(401).json({
        success: false,
        message: 'Access token required'
      });
    }

    console.log('🔍 Verifying token...');

    // Verify token
    const decoded = jwt.verify(token, process.env.JWT_SECRET);

    // Get user from database
    const user = await User.findById(decoded.userId);

    if (!user || !user.isActive) {
      console.log('❌ Invalid token or user not found');
      return res.status(401).json({
        success: false,
        message: 'Invalid token or user not found'
      });
    }

    console.log('✅ Token verified for user:', user.username);

    // Add user info to request object
    req.user = {
      userId: user._id,
      username: user.username,
      email: user.email,
      role: user.role,
      shgGroup: user.shgGroup
    };

    next(); // Continue to next middleware/route

  } catch (error) {
    console.error('❌ Auth middleware error:', error.message);
    res.status(401).json({
      success: false,
      message: 'Invalid token'
    });
  }
};

// Middleware to check if user is admin
const requireAdmin = (req, res, next) => {
  if (req.user.role !== 'admin') {
    console.log('❌ Admin access required for:', req.user.username);
    return res.status(403).json({
      success: false,
      message: 'Admin access required'
    });
  }
  
  console.log('✅ Admin access granted for:', req.user.username);
  next();
};

// Middleware to check if user belongs to same SHG group
const checkSHGAccess = (req, res, next) => {
  // This will be used later for member management
  // Ensures users can only access their own SHG group data
  next();
};

module.exports = {
  authenticateToken,
  requireAdmin,
  checkSHGAccess
};
```

**Explain to Student:**
"This middleware is like a security guard:
1. Checks if user has a valid ID card (JWT token)
2. Verifies the ID card is real and not expired
3. Allows access to protected areas
4. Can check if user is admin for special areas"

---

## 🎯 STEP 2D: Create Authentication Routes (10 minutes)

### Create API endpoints
```javascript
// shg-backend/routes/auth.js
const express = require('express');
const { registerUser, loginUser, getCurrentUser } = require('../controllers/authController');
const { authenticateToken } = require('../middleware/auth');

const router = express.Router();

// @route   POST /api/auth/register
// @desc    Register new user
// @access  Public
router.post('/register', registerUser);

// @route   POST /api/auth/login
// @desc    Login user
// @access  Public
router.post('/login', loginUser);

// @route   GET /api/auth/me
// @desc    Get current user info
// @access  Private (requires token)
router.get('/me', authenticateToken, getCurrentUser);

// Test route to check if auth is working
router.get('/test-protected', authenticateToken, (req, res) => {
  res.json({
    success: true,
    message: 'This is a protected route!',
    user: req.user
  });
});

module.exports = router;
```

### Update server.js to use auth routes
```javascript
// Add this to shg-backend/server.js (after other middleware)

// Import auth routes
const authRoutes = require('./routes/auth');

// Use auth routes
app.use('/api/auth', authRoutes);

// Update the hello route to show available endpoints
app.get('/api/hello', (req, res) => {
  res.json({ 
    message: 'Welcome to SHG Management System!',
    status: 'Server is running perfectly!',
    database: 'Connected to MongoDB',
    endpoints: {
      register: 'POST /api/auth/register',
      login: 'POST /api/auth/login',
      profile: 'GET /api/auth/me',
      testProtected: 'GET /api/auth/test-protected'
    },
    timestamp: new Date().toISOString()
  });
});
```

**Explain to Student:**
"These routes are like different doors to our building:
- `/register` - Sign up for new account
- `/login` - Enter with existing account
- `/me` - Check your profile (need ID card)
- `/test-protected` - Test area (need ID card)"

---

## 🎯 STEP 2E: Test Authentication System (15 minutes)

### Test with Postman or curl commands

#### 1. Test Registration
```bash
curl -X POST http://localhost:5000/api/auth/register \
  -H "Content-Type: application/json" \
  -d '{
    "username": "admin1",
    "email": "<EMAIL>",
    "password": "password123",
    "shgGroup": "Mahila Mandal Group 1",
    "role": "admin"
  }'
```

**Expected Response:**
```json
{
  "success": true,
  "message": "User registered successfully",
  "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "user": {
    "id": "64f1a2b3c4d5e6f7g8h9i0j1",
    "username": "admin1",
    "email": "<EMAIL>",
    "role": "admin",
    "shgGroup": "Mahila Mandal Group 1"
  }
}
```

#### 2. Test Login
```bash
curl -X POST http://localhost:5000/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "password123"
  }'
```

#### 3. Test Protected Route
```bash
# Use the token from login response
curl -X GET http://localhost:5000/api/auth/me \
  -H "Authorization: Bearer YOUR_TOKEN_HERE"
```

### Create a simple test script
```javascript
// shg-backend/test-auth.js
const axios = require('axios');

const BASE_URL = 'http://localhost:5000/api';

async function testAuthentication() {
  try {
    console.log('🧪 Testing Authentication System...\n');

    // Test 1: Register
    console.log('1️⃣ Testing Registration...');
    const registerResponse = await axios.post(`${BASE_URL}/auth/register`, {
      username: 'testuser',
      email: '<EMAIL>',
      password: 'password123',
      shgGroup: 'Test Group',
      role: 'member'
    });
    
    console.log('✅ Registration successful!');
    console.log('Token received:', registerResponse.data.token.substring(0, 20) + '...');
    
    const token = registerResponse.data.token;

    // Test 2: Login
    console.log('\n2️⃣ Testing Login...');
    const loginResponse = await axios.post(`${BASE_URL}/auth/login`, {
      email: '<EMAIL>',
      password: 'password123'
    });
    
    console.log('✅ Login successful!');
    console.log('User:', loginResponse.data.user.username);

    // Test 3: Protected Route
    console.log('\n3️⃣ Testing Protected Route...');
    const protectedResponse = await axios.get(`${BASE_URL}/auth/me`, {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });
    
    console.log('✅ Protected route access successful!');
    console.log('User info:', protectedResponse.data.user.username);

    console.log('\n🎉 All authentication tests passed!');

  } catch (error) {
    console.error('❌ Test failed:', error.response?.data || error.message);
  }
}

// Run tests
testAuthentication();
```

**Run the test:**
```bash
npm install axios
node test-auth.js
```

---

## 🎉 STEP 2 COMPLETE!

### What We Accomplished:
✅ Created User model with password encryption
✅ Built registration and login system
✅ Implemented JWT token authentication
✅ Created security middleware
✅ Set up protected routes
✅ Tested everything works perfectly

### What Student Learned:
1. Database models and validation
2. Password hashing for security
3. JWT tokens for authentication
4. Middleware for route protection
5. API endpoint creation
6. Testing with real HTTP requests

### Next Step Preview:
"In Step 3, we'll create the Member model and build CRUD operations. You'll see how to add, view, edit, and delete SHG members with proper security!"

**Current System Status:**
- Users can register: ✅
- Users can login: ✅
- Protected routes work: ✅
- Ready for member management: ✅
