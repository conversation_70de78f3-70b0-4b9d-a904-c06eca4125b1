{"name": "qs", "description": "A querystring parser that supports nesting and arrays, with a depth limit", "homepage": "https://github.com/ljharb/qs", "version": "6.11.0", "repository": {"type": "git", "url": "https://github.com/ljharb/qs.git"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "main": "lib/index.js", "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "http://ljharb.codes"}], "keywords": ["querystring", "qs", "query", "url", "parse", "stringify"], "engines": {"node": ">=0.6"}, "dependencies": {"side-channel": "^1.0.4"}, "devDependencies": {"@ljharb/eslint-config": "^21.0.0", "aud": "^2.0.0", "browserify": "^16.5.2", "eclint": "^2.8.1", "eslint": "=8.8.0", "evalmd": "^0.0.19", "for-each": "^0.3.3", "has-symbols": "^1.0.3", "iconv-lite": "^0.5.1", "in-publish": "^2.0.1", "mkdirp": "^0.5.5", "npmignore": "^0.3.0", "nyc": "^10.3.2", "object-inspect": "^1.12.2", "qs-iconv": "^1.0.4", "safe-publish-latest": "^2.0.0", "safer-buffer": "^2.1.2", "tape": "^5.5.3"}, "scripts": {"prepack": "npmignore --auto --commentLines=autogenerated", "prepublishOnly": "safe-publish-latest && npm run dist", "prepublish": "not-in-publish || npm run prepublishOnly", "pretest": "npm run --silent readme && npm run --silent lint", "test": "npm run tests-only", "tests-only": "nyc tape 'test/**/*.js'", "posttest": "aud --production", "readme": "evalmd README.md", "postlint": "eclint check $(git ls-files | xargs find 2> /dev/null | grep -vE 'node_modules|\\.git' | grep -v dist/)", "lint": "eslint --ext=js,mjs .", "dist": "mkdirp dist && browserify --standalone Qs lib/index.js > dist/qs.js"}, "license": "BSD-3-<PERSON><PERSON>", "publishConfig": {"ignore": ["!dist/*", "bower.json", "component.json", ".github/workflows"]}}