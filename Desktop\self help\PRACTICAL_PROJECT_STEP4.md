# STEP 4: Financial Tracking - Money Management! 💰

## What We're Building Now
"Now we'll add the financial heart of our SHG system - tracking savings, loans, and transactions. You'll see how to handle money calculations safely and maintain complete financial records!"

---

## 🎯 STEP 4A: Create Transaction Model (15 minutes)

### Create Transaction Database Schema
```javascript
// shg-backend/models/Transaction.js
const mongoose = require('mongoose');

const transactionSchema = new mongoose.Schema({
  transactionId: {
    type: String,
    required: true,
    unique: true,
    uppercase: true
  },
  memberId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Member',
    required: true
  },
  type: {
    type: String,
    enum: ['savings_deposit', 'savings_withdrawal', 'loan_issued', 'loan_repayment', 'interest_payment', 'fine'],
    required: true
  },
  amount: {
    type: Number,
    required: [true, 'Transaction amount is required'],
    min: [0.01, 'Amount must be greater than 0']
  },
  description: {
    type: String,
    maxlength: [200, 'Description cannot exceed 200 characters']
  },
  date: {
    type: Date,
    default: Date.now
  },
  // Balance after this transaction
  balanceAfter: {
    savings: { type: Number, default: 0 },
    loans: { type: Number, default: 0 }
  },
  // Reference to meeting if transaction happened during meeting
  meetingId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Meeting'
  },
  // Who recorded this transaction
  recordedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  // For loan transactions
  loanDetails: {
    loanId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Loan'
    },
    interestRate: Number,
    dueDate: Date
  },
  // Transaction status
  status: {
    type: String,
    enum: ['completed', 'pending', 'cancelled'],
    default: 'completed'
  },
  shgGroup: {
    type: String,
    required: true
  }
}, {
  timestamps: true
});

// Generate transaction ID before saving
transactionSchema.pre('save', async function(next) {
  if (!this.transactionId) {
    try {
      const count = await mongoose.model('Transaction').countDocuments({
        shgGroup: this.shgGroup
      });
      
      const date = new Date().toISOString().slice(0, 10).replace(/-/g, '');
      this.transactionId = `TXN_${this.shgGroup.replace(/\s+/g, '').toUpperCase().substring(0, 4)}_${date}_${(count + 1).toString().padStart(4, '0')}`;
      
      console.log(`💳 Generated Transaction ID: ${this.transactionId}`);
    } catch (error) {
      console.error('Error generating transaction ID:', error);
    }
  }
  next();
});

// Index for better query performance
transactionSchema.index({ memberId: 1, date: -1 });
transactionSchema.index({ shgGroup: 1, type: 1, date: -1 });
transactionSchema.index({ transactionId: 1 });

// Static method to get member's transaction history
transactionSchema.statics.getMemberHistory = async function(memberId, limit = 10) {
  return await this.find({ memberId })
    .sort({ date: -1 })
    .limit(limit)
    .populate('recordedBy', 'username')
    .populate('loanDetails.loanId');
};

// Static method to get group financial summary
transactionSchema.statics.getGroupSummary = async function(shgGroup, startDate, endDate) {
  const matchQuery = {
    shgGroup,
    status: 'completed'
  };
  
  if (startDate && endDate) {
    matchQuery.date = { $gte: startDate, $lte: endDate };
  }
  
  const summary = await this.aggregate([
    { $match: matchQuery },
    {
      $group: {
        _id: '$type',
        totalAmount: { $sum: '$amount' },
        count: { $sum: 1 }
      }
    }
  ]);
  
  return summary;
};

module.exports = mongoose.model('Transaction', transactionSchema);
```

**Explain to Student:**
"This Transaction model is like a detailed receipt book:
- Every money movement is recorded
- Auto-generates unique transaction IDs
- Tracks balance after each transaction
- Links to members and loans
- Supports different transaction types
- Maintains complete audit trail"

---

## 🎯 STEP 4B: Create Loan Model (15 minutes)

### Create Loan Database Schema
```javascript
// shg-backend/models/Loan.js
const mongoose = require('mongoose');

const loanSchema = new mongoose.Schema({
  loanId: {
    type: String,
    required: true,
    unique: true,
    uppercase: true
  },
  memberId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Member',
    required: true
  },
  amount: {
    type: Number,
    required: [true, 'Loan amount is required'],
    min: [100, 'Minimum loan amount is ₹100']
  },
  interestRate: {
    type: Number,
    required: [true, 'Interest rate is required'],
    min: [0, 'Interest rate cannot be negative'],
    max: [50, 'Interest rate cannot exceed 50%']
  },
  issueDate: {
    type: Date,
    default: Date.now
  },
  dueDate: {
    type: Date,
    required: true
  },
  status: {
    type: String,
    enum: ['active', 'paid', 'overdue', 'defaulted'],
    default: 'active'
  },
  // Payment tracking
  totalPaid: {
    type: Number,
    default: 0,
    min: 0
  },
  remainingAmount: {
    type: Number,
    default: function() { return this.amount; }
  },
  monthlyEMI: {
    type: Number,
    required: true
  },
  // Guarantors (other SHG members who guarantee this loan)
  guarantors: [{
    memberId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Member'
    },
    guaranteeAmount: Number
  }],
  // Purpose of loan
  purpose: {
    type: String,
    required: [true, 'Loan purpose is required'],
    enum: ['business', 'education', 'medical', 'agriculture', 'household', 'emergency', 'other']
  },
  purposeDescription: {
    type: String,
    maxlength: [200, 'Purpose description cannot exceed 200 characters']
  },
  // Approval details
  approvedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  approvalDate: {
    type: Date,
    default: Date.now
  },
  // Group info
  shgGroup: {
    type: String,
    required: true
  }
}, {
  timestamps: true
});

// Generate loan ID before saving
loanSchema.pre('save', async function(next) {
  if (!this.loanId) {
    try {
      const count = await mongoose.model('Loan').countDocuments({
        shgGroup: this.shgGroup
      });
      
      const groupCode = this.shgGroup.replace(/\s+/g, '').toUpperCase().substring(0, 4);
      this.loanId = `LOAN_${groupCode}_${(count + 1).toString().padStart(4, '0')}`;
      
      console.log(`🏦 Generated Loan ID: ${this.loanId}`);
    } catch (error) {
      console.error('Error generating loan ID:', error);
    }
  }
  next();
});

// Calculate remaining amount before saving
loanSchema.pre('save', function(next) {
  this.remainingAmount = this.amount - this.totalPaid;
  
  // Update status based on payment
  if (this.remainingAmount <= 0) {
    this.status = 'paid';
  } else if (new Date() > this.dueDate && this.status === 'active') {
    this.status = 'overdue';
  }
  
  next();
});

// Instance method to calculate interest
loanSchema.methods.calculateInterest = function(months) {
  const monthlyRate = this.interestRate / 12 / 100;
  const totalInterest = this.amount * monthlyRate * months;
  return Math.round(totalInterest * 100) / 100; // Round to 2 decimal places
};

// Instance method to calculate EMI
loanSchema.methods.calculateEMI = function(months) {
  const monthlyRate = this.interestRate / 12 / 100;
  const emi = (this.amount * monthlyRate * Math.pow(1 + monthlyRate, months)) / 
              (Math.pow(1 + monthlyRate, months) - 1);
  return Math.round(emi * 100) / 100;
};

// Static method to get overdue loans
loanSchema.statics.getOverdueLoans = async function(shgGroup) {
  const today = new Date();
  return await this.find({
    shgGroup,
    dueDate: { $lt: today },
    status: { $in: ['active', 'overdue'] }
  }).populate('memberId', 'name phoneNumber');
};

// Index for better performance
loanSchema.index({ memberId: 1, status: 1 });
loanSchema.index({ shgGroup: 1, status: 1 });
loanSchema.index({ dueDate: 1, status: 1 });

module.exports = mongoose.model('Loan', loanSchema);
```

**Explain to Student:**
"This Loan model manages all loan operations:
- Tracks loan amounts, interest rates, and due dates
- Calculates EMI and interest automatically
- Supports guarantors (other members who vouch for the loan)
- Monitors payment status and overdue loans
- Links to members and tracks approval process"

---

## 🎯 STEP 4C: Create Financial Controller (25 minutes)

### Create business logic for financial operations
```javascript
// shg-backend/controllers/financialController.js
const Member = require('../models/Member');
const Transaction = require('../models/Transaction');
const Loan = require('../models/Loan');
const mongoose = require('mongoose');

// Record savings deposit
const recordSavingsDeposit = async (req, res) => {
  const session = await mongoose.startSession();
  session.startTransaction();
  
  try {
    console.log('💰 Recording savings deposit...');
    
    const { memberId, amount, description } = req.body;
    
    // Find member
    const member = await Member.findOne({
      _id: memberId,
      shgGroup: req.user.shgGroup,
      isActive: true
    }).session(session);
    
    if (!member) {
      await session.abortTransaction();
      return res.status(404).json({
        success: false,
        message: 'Member not found'
      });
    }
    
    // Update member's savings
    member.savings += parseFloat(amount);
    await member.save({ session });
    
    // Create transaction record
    const transaction = new Transaction({
      memberId,
      type: 'savings_deposit',
      amount: parseFloat(amount),
      description: description || `Savings deposit by ${member.name}`,
      balanceAfter: {
        savings: member.savings,
        loans: member.totalLoans
      },
      recordedBy: req.user.userId,
      shgGroup: req.user.shgGroup
    });
    
    await transaction.save({ session });
    
    await session.commitTransaction();
    
    console.log(`✅ Savings deposit recorded: ₹${amount} for ${member.name}`);
    
    res.json({
      success: true,
      message: 'Savings deposit recorded successfully',
      transaction,
      member: {
        id: member._id,
        name: member.name,
        newSavingsBalance: member.savings
      }
    });
    
  } catch (error) {
    await session.abortTransaction();
    console.error('❌ Error recording savings deposit:', error.message);
    res.status(500).json({
      success: false,
      message: 'Error recording savings deposit',
      error: error.message
    });
  } finally {
    session.endSession();
  }
};

// Issue loan to member
const issueLoan = async (req, res) => {
  const session = await mongoose.startSession();
  session.startTransaction();
  
  try {
    console.log('🏦 Issuing loan...');
    
    const { 
      memberId, 
      amount, 
      interestRate, 
      durationMonths, 
      purpose, 
      purposeDescription,
      guarantors 
    } = req.body;
    
    // Find member
    const member = await Member.findOne({
      _id: memberId,
      shgGroup: req.user.shgGroup,
      isActive: true
    }).session(session);
    
    if (!member) {
      await session.abortTransaction();
      return res.status(404).json({
        success: false,
        message: 'Member not found'
      });
    }
    
    // Check loan eligibility (example: max 4x savings)
    const maxLoanAmount = member.savings * 4;
    if (amount > maxLoanAmount) {
      await session.abortTransaction();
      return res.status(400).json({
        success: false,
        message: `Maximum loan amount is ₹${maxLoanAmount} (4x savings of ₹${member.savings})`
      });
    }
    
    // Calculate due date and EMI
    const dueDate = new Date();
    dueDate.setMonth(dueDate.getMonth() + parseInt(durationMonths));
    
    // Create loan
    const loan = new Loan({
      memberId,
      amount: parseFloat(amount),
      interestRate: parseFloat(interestRate),
      dueDate,
      monthlyEMI: 0, // Will be calculated
      purpose,
      purposeDescription,
      guarantors: guarantors || [],
      approvedBy: req.user.userId,
      shgGroup: req.user.shgGroup
    });
    
    // Calculate EMI
    loan.monthlyEMI = loan.calculateEMI(parseInt(durationMonths));
    await loan.save({ session });
    
    // Update member's total loans
    member.totalLoans += parseFloat(amount);
    await member.save({ session });
    
    // Create transaction record
    const transaction = new Transaction({
      memberId,
      type: 'loan_issued',
      amount: parseFloat(amount),
      description: `Loan issued for ${purpose}`,
      balanceAfter: {
        savings: member.savings,
        loans: member.totalLoans
      },
      loanDetails: {
        loanId: loan._id,
        interestRate: parseFloat(interestRate),
        dueDate
      },
      recordedBy: req.user.userId,
      shgGroup: req.user.shgGroup
    });
    
    await transaction.save({ session });
    
    await session.commitTransaction();
    
    console.log(`✅ Loan issued: ₹${amount} to ${member.name}`);
    
    res.json({
      success: true,
      message: 'Loan issued successfully',
      loan,
      transaction,
      member: {
        id: member._id,
        name: member.name,
        newLoanBalance: member.totalLoans
      }
    });
    
  } catch (error) {
    await session.abortTransaction();
    console.error('❌ Error issuing loan:', error.message);
    res.status(500).json({
      success: false,
      message: 'Error issuing loan',
      error: error.message
    });
  } finally {
    session.endSession();
  }
};

// Record loan repayment
const recordLoanRepayment = async (req, res) => {
  const session = await mongoose.startSession();
  session.startTransaction();
  
  try {
    console.log('💳 Recording loan repayment...');
    
    const { loanId, amount, description } = req.body;
    
    // Find loan
    const loan = await Loan.findOne({
      _id: loanId,
      shgGroup: req.user.shgGroup
    }).populate('memberId').session(session);
    
    if (!loan) {
      await session.abortTransaction();
      return res.status(404).json({
        success: false,
        message: 'Loan not found'
      });
    }
    
    if (loan.status === 'paid') {
      await session.abortTransaction();
      return res.status(400).json({
        success: false,
        message: 'Loan is already fully paid'
      });
    }
    
    const paymentAmount = parseFloat(amount);
    
    // Check if payment exceeds remaining amount
    if (paymentAmount > loan.remainingAmount) {
      await session.abortTransaction();
      return res.status(400).json({
        success: false,
        message: `Payment amount (₹${paymentAmount}) exceeds remaining loan amount (₹${loan.remainingAmount})`
      });
    }
    
    // Update loan
    loan.totalPaid += paymentAmount;
    await loan.save({ session });
    
    // Update member's total loans
    const member = await Member.findById(loan.memberId._id).session(session);
    member.totalLoans -= paymentAmount;
    await member.save({ session });
    
    // Create transaction record
    const transaction = new Transaction({
      memberId: loan.memberId._id,
      type: 'loan_repayment',
      amount: paymentAmount,
      description: description || `Loan repayment for ${loan.loanId}`,
      balanceAfter: {
        savings: member.savings,
        loans: member.totalLoans
      },
      loanDetails: {
        loanId: loan._id,
        interestRate: loan.interestRate
      },
      recordedBy: req.user.userId,
      shgGroup: req.user.shgGroup
    });
    
    await transaction.save({ session });
    
    await session.commitTransaction();
    
    console.log(`✅ Loan repayment recorded: ₹${paymentAmount} for ${loan.memberId.name}`);
    
    res.json({
      success: true,
      message: 'Loan repayment recorded successfully',
      transaction,
      loan: {
        id: loan._id,
        loanId: loan.loanId,
        remainingAmount: loan.remainingAmount,
        status: loan.status
      },
      member: {
        id: member._id,
        name: member.name,
        newLoanBalance: member.totalLoans
      }
    });
    
  } catch (error) {
    await session.abortTransaction();
    console.error('❌ Error recording loan repayment:', error.message);
    res.status(500).json({
      success: false,
      message: 'Error recording loan repayment',
      error: error.message
    });
  } finally {
    session.endSession();
  }
};

// Get member's financial summary
const getMemberFinancialSummary = async (req, res) => {
  try {
    const { memberId } = req.params;
    
    console.log('📊 Getting financial summary for member:', memberId);
    
    // Get member details
    const member = await Member.findOne({
      _id: memberId,
      shgGroup: req.user.shgGroup
    });
    
    if (!member) {
      return res.status(404).json({
        success: false,
        message: 'Member not found'
      });
    }
    
    // Get active loans
    const activeLoans = await Loan.find({
      memberId,
      status: { $in: ['active', 'overdue'] }
    });
    
    // Get recent transactions
    const recentTransactions = await Transaction.getMemberHistory(memberId, 10);
    
    // Calculate totals
    const totalActiveLoans = activeLoans.reduce((sum, loan) => sum + loan.remainingAmount, 0);
    const monthlyEMI = activeLoans.reduce((sum, loan) => sum + loan.monthlyEMI, 0);
    
    res.json({
      success: true,
      member: {
        id: member._id,
        name: member.name,
        memberId: member.memberId,
        savings: member.savings,
        totalLoans: member.totalLoans
      },
      activeLoans,
      summary: {
        totalSavings: member.savings,
        totalActiveLoans,
        monthlyEMI,
        netPosition: member.savings - totalActiveLoans
      },
      recentTransactions
    });
    
  } catch (error) {
    console.error('❌ Error getting financial summary:', error.message);
    res.status(500).json({
      success: false,
      message: 'Error getting financial summary',
      error: error.message
    });
  }
};

// Get group financial dashboard
const getGroupFinancialDashboard = async (req, res) => {
  try {
    console.log('📈 Getting group financial dashboard...');
    
    const { startDate, endDate } = req.query;
    
    // Get group statistics
    const groupStats = await Member.getGroupStats(req.user.shgGroup);
    
    // Get transaction summary
    const transactionSummary = await Transaction.getGroupSummary(
      req.user.shgGroup,
      startDate ? new Date(startDate) : null,
      endDate ? new Date(endDate) : null
    );
    
    // Get overdue loans
    const overdueLoans = await Loan.getOverdueLoans(req.user.shgGroup);
    
    // Get recent transactions
    const recentTransactions = await Transaction.find({
      shgGroup: req.user.shgGroup,
      status: 'completed'
    })
    .sort({ date: -1 })
    .limit(10)
    .populate('memberId', 'name memberId')
    .populate('recordedBy', 'username');
    
    res.json({
      success: true,
      groupStats,
      transactionSummary,
      overdueLoans: {
        count: overdueLoans.length,
        loans: overdueLoans
      },
      recentTransactions
    });
    
  } catch (error) {
    console.error('❌ Error getting dashboard:', error.message);
    res.status(500).json({
      success: false,
      message: 'Error getting dashboard data',
      error: error.message
    });
  }
};

module.exports = {
  recordSavingsDeposit,
  issueLoan,
  recordLoanRepayment,
  getMemberFinancialSummary,
  getGroupFinancialDashboard
};
```

**Explain to Student:**
"This financial controller handles money operations safely:
- Uses database transactions to ensure data consistency
- Validates loan eligibility before issuing
- Tracks all money movements with detailed records
- Calculates EMI and interest automatically
- Provides financial summaries and dashboards
- Handles errors gracefully to prevent data corruption"

---

## 🎯 STEP 4D: Create Financial Routes (10 minutes)

### Create API endpoints for financial operations
```javascript
// shg-backend/routes/financial.js
const express = require('express');
const {
  recordSavingsDeposit,
  issueLoan,
  recordLoanRepayment,
  getMemberFinancialSummary,
  getGroupFinancialDashboard
} = require('../controllers/financialController');
const { authenticateToken, requireAdmin } = require('../middleware/auth');

const router = express.Router();

// All financial routes require authentication
router.use(authenticateToken);

// @route   POST /api/financial/savings/deposit
// @desc    Record savings deposit
// @access  Private (Admin only)
router.post('/savings/deposit', requireAdmin, recordSavingsDeposit);

// @route   POST /api/financial/loans/issue
// @desc    Issue new loan
// @access  Private (Admin only)
router.post('/loans/issue', requireAdmin, issueLoan);

// @route   POST /api/financial/loans/repayment
// @desc    Record loan repayment
// @access  Private (Admin only)
router.post('/loans/repayment', requireAdmin, recordLoanRepayment);

// @route   GET /api/financial/member/:memberId
// @desc    Get member financial summary
// @access  Private
router.get('/member/:memberId', getMemberFinancialSummary);

// @route   GET /api/financial/dashboard
// @desc    Get group financial dashboard
// @access  Private
router.get('/dashboard', getGroupFinancialDashboard);

module.exports = router;
```

### Update server.js to include financial routes
```javascript
// Add this to shg-backend/server.js (after member routes)

// Import financial routes
const financialRoutes = require('./routes/financial');

// Use financial routes
app.use('/api/financial', financialRoutes);
```

---

## 🎉 STEP 4 COMPLETE!

### What We Accomplished:
✅ Created Transaction model for all money movements
✅ Built Loan model with EMI calculations
✅ Implemented savings deposit functionality
✅ Added loan issuance with eligibility checks
✅ Created loan repayment system
✅ Built financial summaries and dashboards
✅ Used database transactions for data consistency
✅ Added comprehensive error handling

### What Student Learned:
1. Financial data modeling
2. Database transactions for consistency
3. Money calculations and validations
4. Loan management systems
5. Financial reporting and dashboards
6. Error handling for financial operations
7. Audit trails for money movements

### Next Step Preview:
"In Step 5, we'll create a beautiful React frontend to interact with our API. You'll see how to build forms, display data, and create a complete user interface!"

**Current System Status:**
- Authentication: ✅
- Member management: ✅
- Financial tracking: ✅
- Ready for frontend: ✅
