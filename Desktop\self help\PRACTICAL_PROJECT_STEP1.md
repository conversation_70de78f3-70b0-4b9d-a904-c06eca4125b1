# STEP 1: Project Setup - Let's Build It Together! 🚀

## What We're Building Today
"Let's create a real SHG Management System from scratch. I'll show you every single step, and you'll see how everything connects!"

---

## 🎯 STEP 1A: Create Project Structure (5 minutes)

### Open Terminal and Create Project
```bash
# Create main project folder
mkdir shg-management-system
cd shg-management-system

# Create backend folder
mkdir shg-backend
cd shg-backend

# Initialize Node.js project
npm init -y
```

**Explain to Student:**
"We just created a folder for our project and told Node.js 'this is a JavaScript project'. The `package.json` file that was created is like a recipe book - it tells our project what ingredients (packages) it needs."

### Install Backend Dependencies
```bash
# Install all the tools we need
npm install express mongoose bcryptjs jsonwebtoken cors dotenv helmet morgan

# Install development tools
npm install --save-dev nodemon
```

**Show the student package.json:**
```json
{
  "name": "shg-backend",
  "version": "1.0.0",
  "description": "",
  "main": "index.js",
  "scripts": {
    "test": "echo \"Error: no test specified\" && exit 1"
  },
  "dependencies": {
    "express": "^4.18.2",
    "mongoose": "^7.5.0",
    "bcryptjs": "^2.4.3",
    "jsonwebtoken": "^9.0.2",
    "cors": "^2.8.5",
    "dotenv": "^16.3.1",
    "helmet": "^7.0.0",
    "morgan": "^1.10.0"
  },
  "devDependencies": {
    "nodemon": "^3.0.1"
  }
}
```

**Explain Each Package:**
- `express`: "Like a waiter in a restaurant - handles requests from customers (frontend)"
- `mongoose`: "Like a translator between our JavaScript and MongoDB database"
- `bcryptjs`: "Like a secret code maker for passwords"
- `jsonwebtoken`: "Like an ID card that proves who you are"
- `cors`: "Allows our frontend and backend to talk to each other"

---

## 🎯 STEP 1B: Create Basic Server (10 minutes)

### Create server.js file
```javascript
// shg-backend/server.js
const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const morgan = require('morgan');

// Create Express app
const app = express();

// Middleware (like security guards and helpers)
app.use(helmet()); // Security guard
app.use(cors()); // Allows frontend to connect
app.use(morgan('combined')); // Logger (keeps track of requests)
app.use(express.json()); // Understands JSON data

// Our first route - like a door to our app
app.get('/api/hello', (req, res) => {
  res.json({ 
    message: 'Welcome to SHG Management System!',
    status: 'Server is running perfectly!',
    timestamp: new Date().toISOString()
  });
});

// Start the server
const PORT = 5000;
app.listen(PORT, () => {
  console.log(`🚀 SHG Management Server is running on http://localhost:${PORT}`);
  console.log(`📝 Test it: http://localhost:${PORT}/api/hello`);
});
```

**Explain to Student:**
"This is our server - think of it like opening a shop. We're telling it:
1. Use security (helmet)
2. Allow customers from anywhere (cors)
3. Keep a log of visitors (morgan)
4. Understand JSON language (express.json)
5. When someone visits `/api/hello`, say welcome!"

### Update package.json scripts
```json
{
  "scripts": {
    "start": "node server.js",
    "dev": "nodemon server.js",
    "test": "echo \"Error: no test specified\" && exit 1"
  }
}
```

### Test the Server
```bash
# Start the server
npm run dev
```

**Show the student:**
1. Terminal shows: "🚀 SHG Management Server is running on http://localhost:5000"
2. Open browser: `http://localhost:5000/api/hello`
3. See JSON response: `{"message": "Welcome to SHG Management System!"}`

**Celebrate:** "Congratulations! You just created your first web server! 🎉"

---

## 🎯 STEP 1C: Add Database Connection (15 minutes)

### Create .env file for secrets
```bash
# Create environment file
touch .env
```

```env
# .env file content
MONGODB_URI=mongodb+srv://username:<EMAIL>/shg_management
JWT_SECRET=your_super_secret_key_make_it_very_long_and_random_12345
PORT=5000
NODE_ENV=development
```

**Explain to Student:**
"The .env file is like a secret diary. We put sensitive information here like database passwords. Never share this file with anyone!"

### Create database connection
```javascript
// shg-backend/config/database.js
const mongoose = require('mongoose');

const connectDB = async () => {
  try {
    console.log('🔄 Connecting to MongoDB...');
    
    const conn = await mongoose.connect(process.env.MONGODB_URI, {
      useNewUrlParser: true,
      useUnifiedTopology: true,
    });
    
    console.log(`✅ MongoDB Connected Successfully!`);
    console.log(`📍 Database Host: ${conn.connection.host}`);
    console.log(`📊 Database Name: ${conn.connection.name}`);
    
  } catch (error) {
    console.error('❌ Database Connection Failed:', error.message);
    console.error('🔧 Please check your MongoDB URI and try again');
    process.exit(1);
  }
};

module.exports = connectDB;
```

### Update server.js to use database
```javascript
// shg-backend/server.js (updated version)
const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const morgan = require('morgan');
require('dotenv').config(); // Load environment variables

const connectDB = require('./config/database');

// Create Express app
const app = express();

// Connect to database
connectDB();

// Middleware
app.use(helmet());
app.use(cors());
app.use(morgan('combined'));
app.use(express.json());

// Test route
app.get('/api/hello', (req, res) => {
  res.json({ 
    message: 'Welcome to SHG Management System!',
    status: 'Server is running perfectly!',
    database: 'Connected to MongoDB',
    timestamp: new Date().toISOString()
  });
});

// Database test route
app.get('/api/db-test', async (req, res) => {
  try {
    const mongoose = require('mongoose');
    const dbStatus = mongoose.connection.readyState;
    
    const statusMap = {
      0: 'Disconnected',
      1: 'Connected',
      2: 'Connecting',
      3: 'Disconnecting'
    };
    
    res.json({
      message: 'Database test successful!',
      status: statusMap[dbStatus],
      database: mongoose.connection.name,
      host: mongoose.connection.host
    });
  } catch (error) {
    res.status(500).json({
      message: 'Database test failed',
      error: error.message
    });
  }
});

// Error handling middleware
app.use((err, req, res, next) => {
  console.error('❌ Server Error:', err.stack);
  res.status(500).json({ 
    message: 'Something went wrong!',
    error: process.env.NODE_ENV === 'development' ? err.message : 'Internal server error'
  });
});

// Start server
const PORT = process.env.PORT || 5000;
app.listen(PORT, () => {
  console.log(`🚀 SHG Management Server is running on http://localhost:${PORT}`);
  console.log(`📝 Test API: http://localhost:${PORT}/api/hello`);
  console.log(`🗄️ Test Database: http://localhost:${PORT}/api/db-test`);
});
```

### Test Database Connection
```bash
# Restart server
npm run dev
```

**Show the student:**
1. Terminal shows database connection messages
2. Visit: `http://localhost:5000/api/db-test`
3. See database status in JSON response

**Explain:** "Now our server can talk to the database! It's like connecting your app to a huge digital filing cabinet where we'll store all the SHG member information."

---

## 🎯 STEP 1D: Create Project Structure (5 minutes)

### Create organized folders
```bash
# Create organized folder structure
mkdir models routes middleware controllers utils

# Create files we'll need
touch models/User.js
touch routes/auth.js
touch middleware/auth.js
touch controllers/authController.js
```

### Show the final structure
```
shg-backend/
├── config/
│   └── database.js
├── models/
│   └── User.js
├── routes/
│   └── auth.js
├── middleware/
│   └── auth.js
├── controllers/
│   └── authController.js
├── utils/
├── .env
├── server.js
└── package.json
```

**Explain to Student:**
"We organize our code like organizing a house:
- `models/` - Database blueprints (like house plans)
- `routes/` - Different doors/entrances to our app
- `middleware/` - Security guards and helpers
- `controllers/` - The actual work gets done here
- `config/` - Settings and configurations"

---

## 🎉 STEP 1 COMPLETE!

### What We Accomplished:
✅ Created project structure
✅ Installed all necessary packages
✅ Built a working web server
✅ Connected to MongoDB database
✅ Organized code in proper folders
✅ Created test endpoints

### What Student Learned:
1. How to set up a Node.js project
2. What each package does and why we need it
3. How Express server works
4. How to connect to a database
5. Proper code organization
6. Environment variables for security

### Next Step Preview:
"In Step 2, we'll create our first database model (User) and build a complete authentication system. You'll see how users can register and login securely!"

**Test Everything Works:**
- Server runs: ✅
- API responds: ✅
- Database connects: ✅
- Ready for Step 2: ✅
