# STEP 3: Member Management - CRUD Operations! 👥

## What We're Building Now
"Now we'll create the heart of our SHG system - managing members! You'll see how to Create, Read, Update, and Delete member records with full database integration."

---

## 🎯 STEP 3A: Create Member Model (15 minutes)

### Create Member Database Schema
```javascript
// shg-backend/models/Member.js
const mongoose = require('mongoose');

const memberSchema = new mongoose.Schema({
  memberId: {
    type: String,
    required: true,
    unique: true,
    uppercase: true
  },
  name: {
    type: String,
    required: [true, 'Member name is required'],
    trim: true,
    minlength: [2, 'Name must be at least 2 characters'],
    maxlength: [50, 'Name cannot exceed 50 characters']
  },
  fatherName: {
    type: String,
    required: [true, 'Father name is required'],
    trim: true
  },
  address: {
    type: String,
    required: [true, 'Address is required'],
    maxlength: [200, 'Address cannot exceed 200 characters']
  },
  phoneNumber: {
    type: String,
    required: [true, 'Phone number is required'],
    unique: true,
    match: [/^[6-9]\d{9}$/, 'Please enter a valid Indian mobile number']
  },
  aadharNumber: {
    type: String,
    required: [true, 'Aadhar number is required'],
    unique: true,
    match: [/^\d{12}$/, 'Aadhar number must be 12 digits']
  },
  bankAccount: {
    accountNumber: {
      type: String,
      match: [/^\d{9,18}$/, 'Invalid account number']
    },
    ifscCode: {
      type: String,
      match: [/^[A-Z]{4}0[A-Z0-9]{6}$/, 'Invalid IFSC code']
    },
    bankName: String
  },
  joinDate: {
    type: Date,
    default: Date.now
  },
  isActive: {
    type: Boolean,
    default: true
  },
  shgGroup: {
    type: String,
    required: true
  },
  savings: {
    type: Number,
    default: 0,
    min: [0, 'Savings cannot be negative']
  },
  totalLoans: {
    type: Number,
    default: 0,
    min: [0, 'Total loans cannot be negative']
  },
  profilePhoto: {
    type: String,
    default: null
  },
  // Track who created/updated this member
  createdBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  updatedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  }
}, {
  timestamps: true // Adds createdAt and updatedAt automatically
});

// Generate member ID before saving
memberSchema.pre('save', async function(next) {
  if (!this.memberId) {
    try {
      // Count existing members in this SHG group
      const count = await mongoose.model('Member').countDocuments({ 
        shgGroup: this.shgGroup 
      });
      
      // Generate member ID: GROUPNAME_001, GROUPNAME_002, etc.
      const groupCode = this.shgGroup.replace(/\s+/g, '').toUpperCase().substring(0, 6);
      this.memberId = `${groupCode}_${(count + 1).toString().padStart(3, '0')}`;
      
      console.log(`🆔 Generated Member ID: ${this.memberId}`);
    } catch (error) {
      console.error('Error generating member ID:', error);
    }
  }
  next();
});

// Update the updatedAt field before saving
memberSchema.pre('save', function(next) {
  this.updatedAt = new Date();
  next();
});

// Create indexes for better search performance
memberSchema.index({ name: 'text', fatherName: 'text', memberId: 'text' });
memberSchema.index({ shgGroup: 1, isActive: 1 });
memberSchema.index({ phoneNumber: 1 });
memberSchema.index({ aadharNumber: 1 });

// Instance method to calculate member's financial summary
memberSchema.methods.getFinancialSummary = function() {
  return {
    memberId: this.memberId,
    name: this.name,
    savings: this.savings,
    totalLoans: this.totalLoans,
    netPosition: this.savings - this.totalLoans,
    joinDate: this.joinDate
  };
};

// Static method to get group statistics
memberSchema.statics.getGroupStats = async function(shgGroup) {
  const stats = await this.aggregate([
    { $match: { shgGroup: shgGroup, isActive: true } },
    {
      $group: {
        _id: null,
        totalMembers: { $sum: 1 },
        totalSavings: { $sum: '$savings' },
        totalLoans: { $sum: '$totalLoans' },
        averageSavings: { $avg: '$savings' }
      }
    }
  ]);
  
  return stats[0] || {
    totalMembers: 0,
    totalSavings: 0,
    totalLoans: 0,
    averageSavings: 0
  };
};

module.exports = mongoose.model('Member', memberSchema);
```

**Explain to Student:**
"This Member model is like a detailed form for each SHG member. Notice:
- Auto-generates unique member IDs (MAHILA_001, MAHILA_002)
- Validates phone numbers and Aadhar numbers
- Tracks who created/updated each record
- Has methods to calculate financial summaries
- Creates database indexes for fast searching"

---

## 🎯 STEP 3B: Create Member Controller (20 minutes)

### Create business logic for member operations
```javascript
// shg-backend/controllers/memberController.js
const Member = require('../models/Member');

// Create new member
const createMember = async (req, res) => {
  try {
    console.log('👤 Creating new member for group:', req.user.shgGroup);
    
    const memberData = {
      ...req.body,
      shgGroup: req.user.shgGroup, // Ensure member belongs to user's group
      createdBy: req.user.userId
    };

    // Check if phone number or Aadhar already exists
    const existingMember = await Member.findOne({
      $or: [
        { phoneNumber: memberData.phoneNumber },
        { aadharNumber: memberData.aadharNumber }
      ]
    });

    if (existingMember) {
      return res.status(400).json({
        success: false,
        message: 'Member with this phone number or Aadhar number already exists'
      });
    }

    const member = new Member(memberData);
    await member.save();

    console.log('✅ Member created successfully:', member.memberId);

    res.status(201).json({
      success: true,
      message: 'Member created successfully',
      member: member
    });

  } catch (error) {
    console.error('❌ Error creating member:', error.message);
    res.status(500).json({
      success: false,
      message: 'Error creating member',
      error: error.message
    });
  }
};

// Get all members with pagination and search
const getMembers = async (req, res) => {
  try {
    const { page = 1, limit = 10, search = '', status = 'active' } = req.query;
    
    console.log(`📋 Getting members for group: ${req.user.shgGroup}, page: ${page}`);

    // Build search query
    const query = {
      shgGroup: req.user.shgGroup,
      isActive: status === 'active' ? true : status === 'inactive' ? false : { $in: [true, false] }
    };

    // Add search functionality
    if (search) {
      query.$or = [
        { name: { $regex: search, $options: 'i' } },
        { memberId: { $regex: search, $options: 'i' } },
        { phoneNumber: { $regex: search, $options: 'i' } },
        { fatherName: { $regex: search, $options: 'i' } }
      ];
    }

    // Calculate pagination
    const skip = (page - 1) * limit;

    // Get members with pagination
    const members = await Member.find(query)
      .sort({ createdAt: -1 }) // Newest first
      .skip(skip)
      .limit(parseInt(limit))
      .populate('createdBy', 'username')
      .populate('updatedBy', 'username');

    // Get total count for pagination
    const total = await Member.countDocuments(query);

    // Get group statistics
    const groupStats = await Member.getGroupStats(req.user.shgGroup);

    res.json({
      success: true,
      members,
      pagination: {
        currentPage: parseInt(page),
        totalPages: Math.ceil(total / limit),
        totalMembers: total,
        hasNext: page * limit < total,
        hasPrev: page > 1
      },
      groupStats,
      searchTerm: search
    });

  } catch (error) {
    console.error('❌ Error getting members:', error.message);
    res.status(500).json({
      success: false,
      message: 'Error retrieving members',
      error: error.message
    });
  }
};

// Get single member by ID
const getMemberById = async (req, res) => {
  try {
    const { id } = req.params;
    
    console.log('🔍 Getting member details for ID:', id);

    const member = await Member.findOne({
      _id: id,
      shgGroup: req.user.shgGroup // Ensure user can only access their group's members
    })
    .populate('createdBy', 'username email')
    .populate('updatedBy', 'username email');

    if (!member) {
      return res.status(404).json({
        success: false,
        message: 'Member not found'
      });
    }

    // Get financial summary
    const financialSummary = member.getFinancialSummary();

    res.json({
      success: true,
      member,
      financialSummary
    });

  } catch (error) {
    console.error('❌ Error getting member:', error.message);
    res.status(500).json({
      success: false,
      message: 'Error retrieving member',
      error: error.message
    });
  }
};

// Update member
const updateMember = async (req, res) => {
  try {
    const { id } = req.params;
    
    console.log('✏️ Updating member:', id);

    // Find member and ensure it belongs to user's group
    const member = await Member.findOne({
      _id: id,
      shgGroup: req.user.shgGroup
    });

    if (!member) {
      return res.status(404).json({
        success: false,
        message: 'Member not found'
      });
    }

    // Check if phone/Aadhar is being changed and already exists
    if (req.body.phoneNumber || req.body.aadharNumber) {
      const existingMember = await Member.findOne({
        _id: { $ne: id }, // Exclude current member
        $or: [
          { phoneNumber: req.body.phoneNumber },
          { aadharNumber: req.body.aadharNumber }
        ]
      });

      if (existingMember) {
        return res.status(400).json({
          success: false,
          message: 'Phone number or Aadhar number already exists'
        });
      }
    }

    // Update member
    const updatedMember = await Member.findByIdAndUpdate(
      id,
      {
        ...req.body,
        updatedBy: req.user.userId
      },
      { new: true, runValidators: true }
    ).populate('updatedBy', 'username');

    console.log('✅ Member updated successfully:', updatedMember.memberId);

    res.json({
      success: true,
      message: 'Member updated successfully',
      member: updatedMember
    });

  } catch (error) {
    console.error('❌ Error updating member:', error.message);
    res.status(500).json({
      success: false,
      message: 'Error updating member',
      error: error.message
    });
  }
};

// Delete member (soft delete)
const deleteMember = async (req, res) => {
  try {
    const { id } = req.params;
    
    console.log('🗑️ Soft deleting member:', id);

    const member = await Member.findOne({
      _id: id,
      shgGroup: req.user.shgGroup
    });

    if (!member) {
      return res.status(404).json({
        success: false,
        message: 'Member not found'
      });
    }

    // Soft delete (set isActive to false)
    member.isActive = false;
    member.updatedBy = req.user.userId;
    await member.save();

    console.log('✅ Member deactivated successfully:', member.memberId);

    res.json({
      success: true,
      message: 'Member deactivated successfully'
    });

  } catch (error) {
    console.error('❌ Error deleting member:', error.message);
    res.status(500).json({
      success: false,
      message: 'Error deleting member',
      error: error.message
    });
  }
};

// Reactivate member
const reactivateMember = async (req, res) => {
  try {
    const { id } = req.params;
    
    console.log('🔄 Reactivating member:', id);

    const member = await Member.findOne({
      _id: id,
      shgGroup: req.user.shgGroup
    });

    if (!member) {
      return res.status(404).json({
        success: false,
        message: 'Member not found'
      });
    }

    member.isActive = true;
    member.updatedBy = req.user.userId;
    await member.save();

    console.log('✅ Member reactivated successfully:', member.memberId);

    res.json({
      success: true,
      message: 'Member reactivated successfully',
      member
    });

  } catch (error) {
    console.error('❌ Error reactivating member:', error.message);
    res.status(500).json({
      success: false,
      message: 'Error reactivating member',
      error: error.message
    });
  }
};

module.exports = {
  createMember,
  getMembers,
  getMemberById,
  updateMember,
  deleteMember,
  reactivateMember
};
```

**Explain to Student:**
"This controller handles all member operations:
- `createMember`: Adds new members with validation
- `getMembers`: Lists members with search and pagination
- `getMemberById`: Gets detailed info for one member
- `updateMember`: Modifies member information
- `deleteMember`: Soft delete (keeps data but marks inactive)
- `reactivateMember`: Brings back deactivated members

Notice how we ensure users can only access their own SHG group's data!"

---

## 🎯 STEP 3C: Create Member Routes (10 minutes)

### Create API endpoints for member operations
```javascript
// shg-backend/routes/members.js
const express = require('express');
const {
  createMember,
  getMembers,
  getMemberById,
  updateMember,
  deleteMember,
  reactivateMember
} = require('../controllers/memberController');
const { authenticateToken, requireAdmin } = require('../middleware/auth');

const router = express.Router();

// All member routes require authentication
router.use(authenticateToken);

// @route   GET /api/members
// @desc    Get all members with search and pagination
// @access  Private
router.get('/', getMembers);

// @route   POST /api/members
// @desc    Create new member
// @access  Private (Admin only)
router.post('/', requireAdmin, createMember);

// @route   GET /api/members/:id
// @desc    Get member by ID
// @access  Private
router.get('/:id', getMemberById);

// @route   PUT /api/members/:id
// @desc    Update member
// @access  Private (Admin only)
router.put('/:id', requireAdmin, updateMember);

// @route   DELETE /api/members/:id
// @desc    Soft delete member
// @access  Private (Admin only)
router.delete('/:id', requireAdmin, deleteMember);

// @route   PUT /api/members/:id/reactivate
// @desc    Reactivate member
// @access  Private (Admin only)
router.put('/:id/reactivate', requireAdmin, reactivateMember);

module.exports = router;
```

### Update server.js to include member routes
```javascript
// Add this to shg-backend/server.js (after auth routes)

// Import member routes
const memberRoutes = require('./routes/members');

// Use member routes
app.use('/api/members', memberRoutes);

// Update hello route to show all available endpoints
app.get('/api/hello', (req, res) => {
  res.json({ 
    message: 'Welcome to SHG Management System!',
    status: 'Server is running perfectly!',
    database: 'Connected to MongoDB',
    endpoints: {
      // Auth endpoints
      register: 'POST /api/auth/register',
      login: 'POST /api/auth/login',
      profile: 'GET /api/auth/me',
      
      // Member endpoints
      getMembers: 'GET /api/members',
      createMember: 'POST /api/members',
      getMember: 'GET /api/members/:id',
      updateMember: 'PUT /api/members/:id',
      deleteMember: 'DELETE /api/members/:id',
      reactivateMember: 'PUT /api/members/:id/reactivate'
    },
    timestamp: new Date().toISOString()
  });
});
```

**Explain to Student:**
"These routes define the URLs for member operations:
- GET `/api/members` - List all members
- POST `/api/members` - Add new member (admin only)
- GET `/api/members/:id` - Get specific member
- PUT `/api/members/:id` - Update member (admin only)
- DELETE `/api/members/:id` - Deactivate member (admin only)

Notice how some operations require admin privileges!"

---

## 🎯 STEP 3D: Test Member Management (15 minutes)

### Create comprehensive test script
```javascript
// shg-backend/test-members.js
const axios = require('axios');

const BASE_URL = 'http://localhost:5000/api';
let authToken = '';

async function testMemberManagement() {
  try {
    console.log('🧪 Testing Member Management System...\n');

    // Step 1: Login as admin to get token
    console.log('1️⃣ Logging in as admin...');
    const loginResponse = await axios.post(`${BASE_URL}/auth/login`, {
      email: '<EMAIL>',
      password: 'password123'
    });
    
    authToken = loginResponse.data.token;
    console.log('✅ Login successful!');

    // Step 2: Create a new member
    console.log('\n2️⃣ Creating new member...');
    const newMember = {
      name: 'Priya Sharma',
      fatherName: 'Ram Sharma',
      address: '123 Main Street, Village ABC',
      phoneNumber: '**********',
      aadharNumber: '**********12',
      bankAccount: {
        accountNumber: '**********',
        ifscCode: 'SBIN0001234',
        bankName: 'State Bank of India'
      }
    };

    const createResponse = await axios.post(`${BASE_URL}/members`, newMember, {
      headers: { 'Authorization': `Bearer ${authToken}` }
    });
    
    console.log('✅ Member created successfully!');
    console.log('Member ID:', createResponse.data.member.memberId);
    console.log('Name:', createResponse.data.member.name);
    
    const memberId = createResponse.data.member._id;

    // Step 3: Get all members
    console.log('\n3️⃣ Getting all members...');
    const membersResponse = await axios.get(`${BASE_URL}/members`, {
      headers: { 'Authorization': `Bearer ${authToken}` }
    });
    
    console.log('✅ Members retrieved successfully!');
    console.log('Total members:', membersResponse.data.pagination.totalMembers);
    console.log('Group stats:', membersResponse.data.groupStats);

    // Step 4: Get specific member
    console.log('\n4️⃣ Getting specific member...');
    const memberResponse = await axios.get(`${BASE_URL}/members/${memberId}`, {
      headers: { 'Authorization': `Bearer ${authToken}` }
    });
    
    console.log('✅ Member details retrieved!');
    console.log('Financial summary:', memberResponse.data.financialSummary);

    // Step 5: Update member
    console.log('\n5️⃣ Updating member...');
    const updateData = {
      savings: 5000,
      address: '456 Updated Street, Village XYZ'
    };

    const updateResponse = await axios.put(`${BASE_URL}/members/${memberId}`, updateData, {
      headers: { 'Authorization': `Bearer ${authToken}` }
    });
    
    console.log('✅ Member updated successfully!');
    console.log('New savings:', updateResponse.data.member.savings);

    // Step 6: Search members
    console.log('\n6️⃣ Searching members...');
    const searchResponse = await axios.get(`${BASE_URL}/members?search=Priya`, {
      headers: { 'Authorization': `Bearer ${authToken}` }
    });
    
    console.log('✅ Search completed!');
    console.log('Found members:', searchResponse.data.members.length);

    // Step 7: Soft delete member
    console.log('\n7️⃣ Deactivating member...');
    await axios.delete(`${BASE_URL}/members/${memberId}`, {
      headers: { 'Authorization': `Bearer ${authToken}` }
    });
    
    console.log('✅ Member deactivated successfully!');

    // Step 8: Reactivate member
    console.log('\n8️⃣ Reactivating member...');
    await axios.put(`${BASE_URL}/members/${memberId}/reactivate`, {}, {
      headers: { 'Authorization': `Bearer ${authToken}` }
    });
    
    console.log('✅ Member reactivated successfully!');

    console.log('\n🎉 All member management tests passed!');

  } catch (error) {
    console.error('❌ Test failed:', error.response?.data || error.message);
  }
}

// Run tests
testMemberManagement();
```

**Run the test:**
```bash
node test-members.js
```

---

## 🎉 STEP 3 COMPLETE!

### What We Accomplished:
✅ Created comprehensive Member model with validation
✅ Built full CRUD operations for members
✅ Implemented search and pagination
✅ Added group-based access control
✅ Created financial summary methods
✅ Implemented soft delete functionality
✅ Added member reactivation feature
✅ Tested all operations thoroughly

### What Student Learned:
1. Database schema design with validation
2. CRUD operations (Create, Read, Update, Delete)
3. Search functionality with regex
4. Pagination for large datasets
5. Access control and security
6. Soft delete vs hard delete
7. Database relationships and population
8. API testing and validation

### Database Integration Points Covered:
- **Model Creation**: Defined member structure
- **Data Validation**: Phone, Aadhar, email validation
- **Indexing**: For fast search performance
- **Relationships**: Links to User model
- **Aggregation**: Group statistics calculation
- **Query Optimization**: Pagination and filtering

### Next Step Preview:
"In Step 4, we'll add financial tracking - savings deposits, loan management, and transaction history. You'll see how to handle money calculations and maintain financial records!"

**Current System Status:**
- User authentication: ✅
- Member management: ✅
- Search and pagination: ✅
- Access control: ✅
- Ready for financial features: ✅
